# Gemini CLI

[![Gemini CLI CI](https://github.com/inkbytefo/gemini-cli-compatible/actions/workflows/ci.yml/badge.svg)](https://github.com/inkbytefo/gemini-cli-compatible/actions/workflows/ci.yml)

![Gemini CLI Screenshot](./docs/assets/gemini-screenshot.png)

This repository contains Gemini CLI, an enhanced command-line AI workflow tool based on Google's Gemini CLI that connects to your
tools, understands your code and accelerates your workflows with support for multiple AI providers.

**Gemini CLI** is a fork of the original Gemini CLI with enhanced features and multi-provider support, developed by inkbytefo.

With Gemini CLI you can:

- Query and edit large codebases with enhanced AI capabilities and multiple provider support.
- Generate new apps from PDFs or sketches, using multimodal AI capabilities.
- Automate operational tasks, like querying pull requests or handling complex rebases.
- Use tools and MCP servers to connect new capabilities.
- Work with multiple AI providers: Gemini, OpenAI, Anthropic, Mistral, OpenRouter, and custom endpoints.
- Enhanced workflow automation with improved provider compatibility.

## Quickstart

You have multiple options to install S647.

### With Node

1. **Prerequisites:** Ensure you have [Node.js version 20](https://nodejs.org/en/download) or higher installed.
2. **Run the CLI:** Execute the following command in your terminal:

   ```bash
   npx https://github.com/inkbytefo/s647
   ```

   Or install it with:

   ```bash
   npm install -g @inkbytefo/s647
   ```

   Then, run the CLI from anywhere:

   ```bash
   s647
   ```

### With Homebrew

1. **Prerequisites:** Ensure you have [Homebrew](https://brew.sh/) installed.
2. **Install the CLI** Execute the following command in your terminal:

   ```bash
   brew install s647
   ```

   Then, run the CLI from anywhere:

   ```bash
   s647
   ```

### Common Configuration steps

3. **Pick a color theme**
4. **Configure your AI provider:** S647 supports multiple AI providers. You can use Gemini, OpenAI, Anthropic, Mistral, OpenRouter, or custom endpoints.

You are now ready to use S647!

### Use a Gemini API key:

The Gemini API provides a free tier with [100 requests per day](https://ai.google.dev/gemini-api/docs/rate-limits#free-tier) using Gemini 2.5 Pro, control over which model you use, and access to higher rate limits (with a paid plan):

1. Generate a key from [Google AI Studio](https://aistudio.google.com/apikey).
2. Set it as an environment variable in your terminal. Replace `YOUR_API_KEY` with your generated key.

   ```bash
   export GEMINI_API_KEY="YOUR_API_KEY"
   ```

3. (Optionally) Upgrade your Gemini API project to a paid plan on the API key page (will automatically unlock [Tier 1 rate limits](https://ai.google.dev/gemini-api/docs/rate-limits#tier-1))

### Use a Vertex AI API key:

The Vertex AI API provides a [free tier](https://cloud.google.com/vertex-ai/generative-ai/docs/start/express-mode/overview) using express mode for Gemini 2.5 Pro, control over which model you use, and access to higher rate limits with a billing account:

1. Generate a key from [Google Cloud](https://cloud.google.com/vertex-ai/generative-ai/docs/start/api-keys).
2. Set it as an environment variable in your terminal. Replace `YOUR_API_KEY` with your generated key and set GOOGLE_GENAI_USE_VERTEXAI to true

   ```bash
   export GOOGLE_API_KEY="YOUR_API_KEY"
   export GOOGLE_GENAI_USE_VERTEXAI=true
   ```

3. (Optionally) Add a billing account on your project to get access to [higher usage limits](https://cloud.google.com/vertex-ai/generative-ai/docs/quotas)

### Use OpenAI Compatible Providers:

Gemini CLI supports multiple AI providers including OpenAI, Anthropic, Mistral, OpenRouter, and custom endpoints.

#### **Quick Setup with .env file (Recommended):**

1. **Copy the example environment file:**

   ```bash
   cp .env.example .env
   ```

2. **Edit .env and set your preferred provider and API key:**

   ```bash
   # Set default provider
   GEMINI_PROVIDER=openai
   GEMINI_MODEL=gpt-4

   # Add your API key
   OPENAI_API_KEY=your_openai_api_key_here
   ```

3. **Run Gemini CLI (will use .env settings automatically):**
   ```bash
   gemini "Hello, how are you?"
   ```

#### **Manual Setup:**

1. **OpenAI**: Set your OpenAI API key and use the `--provider openai` flag:

   ```bash
   export OPENAI_API_KEY="YOUR_OPENAI_API_KEY"
   s647 --provider openai --model gpt-4
   ```

2. **Anthropic**: Set your Anthropic API key and use the `--provider anthropic` flag:

   ```bash
   export ANTHROPIC_API_KEY="YOUR_ANTHROPIC_API_KEY"
   s647 --provider anthropic --model claude-3-sonnet-20240229
   ```

3. **Mistral**: Set your Mistral API key and use the `--provider mistral` flag:

   ```bash
   export MISTRAL_API_KEY="YOUR_MISTRAL_API_KEY"
   s647 --provider mistral --model mistral-large-latest
   ```

4. **OpenRouter**: Set your OpenRouter API key and use the `--provider openrouter` flag:

   ```bash
   export OPENROUTER_API_KEY="YOUR_OPENROUTER_API_KEY"
   s647 --provider openrouter --model anthropic/claude-3-sonnet
   ```

5. **Custom Provider**: Set your custom API key and base URL, then use the `--provider custom` flag:
   ```bash
   export CUSTOM_API_KEY="YOUR_CUSTOM_API_KEY"
   export CUSTOM_BASE_URL="https://your-custom-endpoint.com/v1"
   s647 --provider custom --model your-model-name
   ```

#### **Auto-Detection:**

If you don't specify a provider, Gemini CLI will automatically detect which provider to use based on available API keys in your environment.

For other authentication methods, including Google Workspace accounts, see the [authentication](./docs/cli/authentication.md) guide.

## Examples

Once the CLI is running, you can start interacting with Gemini CLI from your shell.

You can start a project from a new directory:

```sh
cd new-project/
gemini
> Write me a Discord bot that answers questions using a FAQ.md file I will provide
```

Or work with an existing project:

```sh
git clone https://github.com/inkbytefo/gemini-cli-compatible
cd gemini-cli-compatible
gemini
> Give me a summary of all of the changes that went in yesterday
```

### Next steps

- Learn how to [contribute to or build from the source](./CONTRIBUTING.md).
- Explore the available **[CLI Commands](./docs/cli/commands.md)**.
- If you encounter any issues, review the **[troubleshooting guide](./docs/troubleshooting.md)**.
- For more comprehensive documentation, see the [full documentation](./docs/index.md).
- Take a look at some [popular tasks](#popular-tasks) for more inspiration.
- Check out our **[Official Roadmap](./ROADMAP.md)**

### Troubleshooting

Head over to the [troubleshooting guide](docs/troubleshooting.md) if you're
having issues.

## Popular tasks

### Explore a new codebase

Start by `cd`ing into an existing or newly-cloned repository and running `s647`.

```text
> Describe the main pieces of this system's architecture.
```

```text
> What security mechanisms are in place?
```

### Work with your existing code

```text
> Implement a first draft for GitHub issue #123.
```

```text
> Help me migrate this codebase to the latest version of Java. Start with a plan.
```

### Automate your workflows

Use MCP servers to integrate your local system tools with your enterprise collaboration suite.

```text
> Make me a slide deck showing the git history from the last 7 days, grouped by feature and team member.
```

```text
> Make a full-screen web app for a wall display to show our most interacted-with GitHub issues.
```

### Interact with your system

```text
> Convert all the images in this directory to png, and rename them to use dates from the exif data.
```

```text
> Organize my PDF invoices by month of expenditure.
```

### Uninstall

Head over to the [Uninstall](docs/Uninstall.md) guide for uninstallation instructions.

## Terms of Service and Privacy Notice

For details on the terms of service and privacy notice applicable to your use of S647, see the [Terms of Service and Privacy Notice](./docs/tos-privacy.md).
