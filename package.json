{"name": "@inkbytefo/s647", "version": "1.0.0", "engines": {"node": ">=20.0.0"}, "type": "module", "workspaces": ["packages/*"], "private": "true", "repository": {"type": "git", "url": "git+https://github.com/inkbytefo/s647.git"}, "config": {"sandboxImageUri": "us-docker.pkg.dev/s647-dev/s647/sandbox:1.0.0"}, "scripts": {"start": "node scripts/start.js", "dev": "npm run build && npm run start", "dev:watch": "concurrently \"npm run build -- --watch\" \"nodemon --watch packages/*/dist --exec 'npm run start'\"", "debug": "cross-env DEBUG=1 node --inspect-brk scripts/start.js", "auth:npm": "npx google-artifactregistry-auth", "auth:docker": "gcloud auth configure-docker us-west1-docker.pkg.dev", "auth": "npm run auth:npm && npm run auth:docker", "generate": "node scripts/generate-git-commit-info.js", "build": "node scripts/build.js", "build:all": "npm run build && npm run build:sandbox", "build:packages": "npm run build --workspaces", "build:sandbox": "node scripts/build_sandbox.js --skip-npm-install-build", "bundle": "npm run generate && node esbuild.config.js && node scripts/copy_bundle_assets.js", "test": "npm run test --workspaces", "test:ci": "npm run test:ci --workspaces --if-present && npm run test:scripts", "test:scripts": "vitest run --config ./scripts/tests/vitest.config.ts", "test:e2e": "npm run test:integration:sandbox:none -- --verbose --keep-output", "test:integration:all": "npm run test:integration:sandbox:none && npm run test:integration:sandbox:docker && npm run test:integration:sandbox:podman", "test:integration:sandbox:none": "S647_SANDBOX=false node integration-tests/run-tests.js", "test:integration:sandbox:docker": "S647_SANDBOX=docker node integration-tests/run-tests.js", "test:integration:sandbox:podman": "S647_SANDBOX=podman node integration-tests/run-tests.js", "lint": "eslint . --ext .ts,.tsx && eslint integration-tests", "lint:fix": "eslint . --fix && eslint integration-tests --fix", "lint:ci": "eslint . --ext .ts,.tsx --max-warnings 0 && eslint integration-tests --max-warnings 0", "format": "prettier --write .", "typecheck": "npm run typecheck --workspaces --if-present", "preflight": "npm run clean && npm ci && npm run format && npm run lint:ci && npm run build && npm run typecheck && npm run test:ci", "prepare": "npm run bundle", "prepare:package": "node scripts/prepare-package.js", "release:version": "node scripts/version.js", "telemetry": "node scripts/telemetry.js", "clean": "node scripts/clean.js", "dev:test": "node scripts/dev-test.js", "dev:openai": "node scripts/dev-test.js openai gpt-3.5-turbo", "dev:anthropic": "node scripts/dev-test.js anthropic claude-3-sonnet", "dev:mistral": "node scripts/dev-test.js mistral mistral-large", "dev:custom": "node scripts/dev-test.js custom mock-model"}, "bin": {"s647": "bundle/s647.js"}, "files": ["bundle/", "README.md", "LICENSE"], "devDependencies": {"@types/diff": "^7.0.2", "@types/glob": "^8.1.0", "@types/html-to-text": "^9.0.4", "@types/micromatch": "^4.0.9", "@types/mime-types": "^3.0.1", "@types/minimatch": "^5.1.2", "@types/mock-fs": "^4.13.4", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/shell-quote": "^1.7.5", "@vitest/coverage-v8": "^3.1.1", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "esbuild": "^0.25.0", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-license-header": "^0.8.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "glob": "^10.4.5", "globals": "^16.0.0", "json": "^11.0.0", "lodash": "^4.17.21", "memfs": "^4.17.2", "mock-fs": "^5.5.0", "prettier": "^3.5.3", "react-devtools-core": "^4.28.5", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "vitest": "^3.2.4", "yargs": "^17.7.2"}, "dependencies": {"@google/genai": "^1.10.0", "@modelcontextprotocol/sdk": "^1.16.0", "@opentelemetry/api": "^1.9.0", "@opentelemetry/api-logs": "^0.203.0", "@opentelemetry/exporter-logs-otlp-grpc": "^0.203.0", "@opentelemetry/exporter-metrics-otlp-grpc": "^0.203.0", "@opentelemetry/exporter-trace-otlp-grpc": "^0.203.0", "@opentelemetry/instrumentation-http": "^0.203.0", "@opentelemetry/otlp-exporter-base": "^0.203.0", "@opentelemetry/resources": "^2.0.1", "@opentelemetry/sdk-logs": "^0.203.0", "@opentelemetry/sdk-metrics": "^2.0.1", "@opentelemetry/sdk-node": "^0.203.0", "@opentelemetry/sdk-trace-node": "^2.0.1", "@opentelemetry/semantic-conventions": "^1.36.0", "ajv": "^8.17.1", "chardet": "^2.1.0", "diff": "^8.0.2", "google-auth-library": "^10.1.0", "html-to-text": "^9.0.5", "https-proxy-agent": "^7.0.6", "ignore": "^7.0.5", "ink": "^6.0.1", "mime-types": "^3.0.1", "open": "^10.2.0", "react": "^19.1.0", "shell-quote": "^1.8.3", "simple-git": "^3.28.0", "undici": "^7.12.0"}}